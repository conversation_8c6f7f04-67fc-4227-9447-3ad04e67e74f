<?php

declare(strict_types=1);

namespace App\Policies;

use App\Models\User;

/**
 * Report Policy
 *
 * Controls access to report functionality based on the existing four-tier permission system:
 * - Root/Admin: Can view all organization reports
 * - Organisation Owner: Can view reports for their organizations
 * - Organisation Member: Can view basic reports for their organizations
 *
 * Report data is filtered by organization permissions in the service layer.
 */
final class ReportPolicy
{
    /**
     * Determine whether the user can view any reports
     */
    public function viewAny(User $user): bool
    {
        // System admins can view all reports
        if ($user->hasSystemAdminAccess()) {
            return true;
        }

        // Organization users can view reports if they belong to at least one organization
        $organisationIds = $user->getOrganisationIds();
        return !$organisationIds->isEmpty();
    }

    /**
     * Determine whether the user can access reports for specific organisations
     */
    public function viewForOrganisations(User $user, array $organisationIds): bool
    {
        // System admins can access any organisation's reports
        if ($user->hasSystemAdminAccess()) {
            return true;
        }

        // Non-system users can only access one organisation at a time
        if (count($organisationIds) > 1) {
            return false;
        }

        // Check if user belongs to the specified organisation
        $organisationId = $organisationIds[0];
        return $user->belongsToOrganisation($organisationId);
    }

    /**
     * Determine whether the user can view sales reports
     */
    public function viewSales(User $user): bool
    {
        return $this->viewAny($user);
    }

    /**
     * Determine whether the user can view sales reports for specific organisations
     */
    public function viewSalesForOrganisations(User $user, array $organisationIds): bool
    {
        return $this->viewSales($user) && $this->viewForOrganisations($user, $organisationIds);
    }

    /**
     * Determine whether the user can view volume reports
     */
    public function viewVolume(User $user): bool
    {
        return $this->viewAny($user);
    }

    /**
     * Determine whether the user can view volume reports for specific organisations
     */
    public function viewVolumeForOrganisations(User $user, array $organisationIds): bool
    {
        return $this->viewVolume($user) && $this->viewForOrganisations($user, $organisationIds);
    }

    /**
     * Determine whether the user can view refund reports
     */
    public function viewRefunds(User $user): bool
    {
        return $this->viewAny($user);
    }

    /**
     * Determine whether the user can view refund reports for specific organisations
     */
    public function viewRefundsForOrganisations(User $user, array $organisationIds): bool
    {
        return $this->viewRefunds($user) && $this->viewForOrganisations($user, $organisationIds);
    }

    /**
     * Determine whether the user can view order status reports
     */
    public function viewOrderStatus(User $user): bool
    {
        return $this->viewAny($user);
    }

    /**
     * Determine whether the user can view order status reports for specific organisations
     */
    public function viewOrderStatusForOrganisations(User $user, array $organisationIds): bool
    {
        return $this->viewOrderStatus($user) && $this->viewForOrganisations($user, $organisationIds);
    }

    /**
     * Determine whether the user can export reports
     *
     * Export functionality is restricted to organization owners and system admins
     * to prevent potential data exposure and system resource abuse.
     */
    public function export(User $user): bool
    {
        // System admins can export all reports
        if ($user->hasSystemAdminAccess()) {
            return true;
        }

        // Only organization owners can export reports
        return $user->hasOwnerRoleInAnyOrganisation();
    }

    /**
     * Determine whether the user can export reports for specific organisations
     */
    public function exportForOrganisations(User $user, array $organisationIds): bool
    {
        return $this->export($user) && $this->viewForOrganisations($user, $organisationIds);
    }

    /**
     * Determine whether the user can view detailed financial data
     *
     * Some reports may contain sensitive financial information that should
     * only be accessible to organization owners and system admins.
     */
    public function viewDetailedFinancials(User $user): bool
    {
        // System admins can view all detailed financial data
        if ($user->hasSystemAdminAccess()) {
            return true;
        }

        // Only organization owners can view detailed financial data
        return $user->hasOwnerRoleInAnyOrganisation();
    }

    /**
     * Determine whether the user can access cross-organization reports
     *
     * Cross-organization reports are only available to system admins.
     */
    public function viewCrossOrganization(User $user): bool
    {
        return $user->hasSystemAdminAccess();
    }

    /**
     * Determine whether the user can access real-time reports
     *
     * Real-time reports may have higher system resource requirements.
     */
    public function viewRealTime(User $user): bool
    {
        // System admins can access real-time reports
        if ($user->hasSystemAdminAccess()) {
            return true;
        }

        // Organization owners can access real-time reports for their organizations
        return $user->hasOwnerRoleInAnyOrganisation();
    }

    /**
     * Determine whether the user can schedule automated reports
     */
    public function scheduleReports(User $user): bool
    {
        // System admins can schedule any reports
        if ($user->hasSystemAdminAccess()) {
            return true;
        }

        // Organization owners can schedule reports for their organizations
        return $user->hasOwnerRoleInAnyOrganisation();
    }

    /**
     * Determine whether the user can access historical data beyond a certain period
     *
     * Historical data access may be restricted based on user role.
     */
    public function viewHistoricalData(User $user, int $monthsBack = 12): bool
    {
        // System admins can access all historical data
        if ($user->hasSystemAdminAccess()) {
            return true;
        }

        // Organization owners can access extended historical data
        if ($user->hasOwnerRoleInAnyOrganisation()) {
            return $monthsBack <= 24; // 2 years for owners
        }

        // Organization members have limited historical access
        return $monthsBack <= 6; // 6 months for members
    }

    /**
     * Determine whether the user can access aggregated data across multiple time periods
     */
    public function viewAggregatedData(User $user): bool
    {
        return $this->viewAny($user);
    }

    /**
     * Determine whether the user can access custom report configurations
     */
    public function createCustomReports(User $user): bool
    {
        // System admins can create any custom reports
        if ($user->hasSystemAdminAccess()) {
            return true;
        }

        // Organization owners can create custom reports for their organizations
        return $user->hasOwnerRoleInAnyOrganisation();
    }
}
