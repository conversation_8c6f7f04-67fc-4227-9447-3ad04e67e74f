<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1;

use App\Http\Requests\ReportFilterRequest;
use App\Http\Requests\ReportExportRequest;
use App\Http\Resources\Api\V1\SalesReportResource;
use App\Http\Resources\Api\V1\VolumeReportResource;
use App\Http\Resources\Api\V1\RefundReportResource;
use App\Http\Resources\Api\V1\OrderStatusReportResource;
use App\Services\ReportService;
use Illuminate\Http\JsonResponse;

/**
 * Report Controller
 *
 * Handles data reporting endpoints including sales, volume, refunds, and order status reports.
 * Implements role-based access control based on the existing four-tier permission system.
 */
final class ReportController extends ApiController
{
    public function __construct(
        private readonly ReportService $reportService
    ) {
        // Apply authorization policy for all report methods
        $this->middleware('auth:sanctum');
    }

    /**
     * Get sales report data
     *
     * Returns sales amount statistics grouped by time period and region
     */
    public function sales(ReportFilterRequest $request): JsonResponse
    {
        $this->authorize('viewSalesForOrganisations', ['report', $request->input('organisation_ids')]);

        $data = $this->reportService->getSalesReport($request->getProcessedData());

        return $this->successResponse(
            new SalesReportResource($data),
            'api.reports.sales_retrieved'
        );
    }

    /**
     * Get volume report data
     *
     * Returns sales volume statistics grouped by time period and region
     */
    public function volume(ReportFilterRequest $request): JsonResponse
    {
        $this->authorize('viewVolumeForOrganisations', ['report', $request->input('organisation_ids')]);

        $data = $this->reportService->getVolumeReport($request->getProcessedData());

        return $this->successResponse(
            new VolumeReportResource($data),
            'api.reports.volume_retrieved'
        );
    }

    /**
     * Get refund analysis report
     *
     * Returns refund statistics and analysis data
     */
    public function refunds(ReportFilterRequest $request): JsonResponse
    {
        $this->authorize('viewRefundsForOrganisations', ['report', $request->input('organisation_ids')]);

        $data = $this->reportService->getRefundReport($request->getProcessedData());

        return $this->successResponse(
            new RefundReportResource($data),
            'api.reports.refunds_retrieved'
        );
    }

    /**
     * Get order status report
     *
     * Returns order status distribution and statistics
     */
    public function orderStatus(ReportFilterRequest $request): JsonResponse
    {
        $this->authorize('viewOrderStatusForOrganisations', ['report', $request->input('organisation_ids')]);

        $data = $this->reportService->getOrderStatusReport($request->getProcessedData());

        return $this->successResponse(
            new OrderStatusReportResource($data),
            'api.reports.order_status_retrieved'
        );
    }

    /**
     * Export report data
     *
     * Generates and returns downloadable report files
     */
    public function export(ReportExportRequest $request): JsonResponse
    {
        $this->authorize('exportForOrganisations', ['report', $request->input('organisation_ids')]);

        $result = $this->reportService->exportReport($request->validated());

        return $this->successResponse(
            $result,
            'api.reports.export_started'
        );
    }
}
